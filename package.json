{"name": "sentrycoin-flash-crash-predictor", "version": "1.0.0", "description": "SentryCoin Flash Crash Predictor - Real-time order book imbalance detection engine", "main": "src/index.js", "type": "module", "scripts": {"start": "node src/index.js", "start:multi": "node src/multi-symbol-predictor.js", "dev": "node --watch src/index.js", "dev:multi": "node --watch src/multi-symbol-predictor.js", "test": "node src/test.js", "backtest": "node run-backtest.js", "backtest:fetch": "node run-backtest.js fetch", "backtest:test": "node run-backtest.js test", "backtest:quick": "node run-backtest.js quick", "connectivity": "node src/connectivity-test.js", "build": "echo 'No build step required for Node.js'", "validation-report": "node view-validation-report.js"}, "dependencies": {"ws": "^8.14.2", "node-telegram-bot-api": "^0.64.0", "dotenv": "^16.3.1", "axios": "^1.6.2", "express": "^4.18.2"}, "devDependencies": {"nodemon": "^3.0.2"}, "keywords": ["crypto", "flash-crash", "order-book", "quantitative", "trading", "binance", "market-microstructure", "liquidity-analysis"], "author": "Sentry<PERSON><PERSON>n", "license": "MIT", "engines": {"node": ">=18.0.0"}}